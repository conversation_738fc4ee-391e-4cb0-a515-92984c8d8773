/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    DateTimePicker: typeof import('./../components/DateTimePicker/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SvgIcon: typeof import('./../components/SvgIcon/index.vue')['default']
    Tabbar: typeof import('./../components/Tabbar/index.vue')['default']
    VanButton: typeof import('vant/es')['Button']
    VanCell: typeof import('vant/es')['Cell']
    VanConfigProvider: typeof import('vant/es')['ConfigProvider']
    VanField: typeof import('vant/es')['Field']
    VanForm: typeof import('vant/es')['Form']
    VanIcon: typeof import('vant/es')['Icon']
    VanList: typeof import('vant/es')['List']
    VanPopup: typeof import('vant/es')['Popup']
    VanSwipeCell: typeof import('vant/es')['SwipeCell']
    VanTabbar: typeof import('vant/es')['Tabbar']
    VanTabbarItem: typeof import('vant/es')['TabbarItem']
  }
}
