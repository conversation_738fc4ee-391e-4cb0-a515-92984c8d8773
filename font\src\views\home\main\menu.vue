<template>
  <div class="menuBox">
    <div
      v-for="menuItem in menuButtons"
      :key="menuItem.text"
      class="w-1/3 flex flex-col justify-center items-center"
      style="height: 80px"
      @click="menuItem.func"
    >
      {{ menuItem.text }}
    </div>
  </div>
</template>
<script setup lang="ts">
import { useData } from "@/hooks/useData";
import api from "@/api/index";
import type { Profile } from "@/views/profile/profile";

const router = useRouter();

// 获取默认档案
const {
  data: defaultProfileData,
  execute: executeGetDefaultProfile,
  onFetchResponse: onFetchDefaultProfile,
  onFetchError: onFetchDefaultProfileError
} = useData(api.profile.getDefaultProfile);

// 处理"我的八字"点击事件
const handleMyBaziClick = async () => {
  // 设置成功回调
  onFetchDefaultProfile((profile: Profile) => {
    if (profile && profile.profileId) {
      // 有默认档案，跳转到八字排盘结果页面
      router.push({
        name: "BaziResult",
        query: { profileId: profile.profileId }
      });
    } else {
      // 没有默认档案，跳转到创建档案页面
      router.push({ name: "createPaipan" });
    }
  });

  // 设置错误回调
  onFetchDefaultProfileError((error: any) => {
    // 如果获取默认档案失败，也跳转到创建档案页面
    console.error("获取默认档案失败:", error);
    router.push({ name: "createPaipan" });
  });

  // 执行请求
  await executeGetDefaultProfile({});
};

const menuButtons = [
  {
    text: "我的档案",
    icon: "",
    func: () => {
      router.push({ name: "Profile" });
    }
  },
  {
    text: "我的运势",
    icon: "",
    func: () => { }
  },
  {
    text: "缘分合盘",
    icon: "",
    func: () => { }
  },
  {
    text: "流年财运",
    icon: "",
    func: () => { }
  },
  {
    text: "爱情运势",
    icon: "",
    func: () => { }
  },
  {
    text: "我的八字",
    icon: "",
    func: handleMyBaziClick
  }
];
</script>

<style scoped>
.menuBox {
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  align-self: stretch;
  padding: 0px 16px;
  border-radius: 12px;
  opacity: 1;
  background:
    linear-gradient(0deg, rgba(0, 0, 0, 0.001), rgba(0, 0, 0, 0.001)), #ffffff;
  box-sizing: border-box;
  border: 1px solid #e5e7eb;
  backdrop-filter: blur(6px);
  box-shadow: 0px 10px 15px -3px rgba(0, 0, 0, 0.1);
}
</style>
