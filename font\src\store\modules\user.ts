import { defineStore } from "pinia";
import type { Info } from "@/views/user/user";

export const useUserStore = defineStore("phone-user-info", {
  state: () => ({
    userInfo: {} as Info
  }),
  actions: {
    setUserInfo(data: Info) {
      this.userInfo = data;
      this.userInfo.vip = {
        isVip: false,
        endTime: ""
      };
    },
    clearUserInfo() {
      this.userInfo = {} as Info;
      localStorage.removeItem("Authorization");
    }
  },
  persist: true
});
