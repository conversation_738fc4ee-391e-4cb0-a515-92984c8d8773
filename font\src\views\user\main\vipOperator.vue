<template>
  <div class="box">
    <div class="flex justify-between items-center">
      <span>logo</span>
      <button v-if="!isVipUser" class="buyButton">
        {{ $t("user.vip.open") }}
      </button>
      <button v-else class="buyButton">{{ $t("user.vip.renew") }}</button>
    </div>

    <div style="opacity: 0.9">{{ $t("user.vip.description") }}</div>
    <div class="flex gap-2">
      <button v-for="buttonItem in vipFunction" class="functionButton">
        {{ buttonItem.text }}
      </button>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useUserStore } from "@/store/modules/user";
import test from "node:test";
const store = useUserStore();

const { userInfo } = storeToRefs(store);
const { t } = useI18n();

const vipFunction = [
  {
    text: t("user.vip.fortune")
  },
  {
    text: t("user.vip.love")
  },
  {
    text: t("user.vip.knowledge")
  }
];
</script>
<style scoped>
.box {
  border-radius: 16px;
  opacity: 1;
  background:
    linear-gradient(0deg, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0)),
    linear-gradient(135deg, #92abff -8%, #7ccffc 67%);
  padding: 16px;
  color: #ffffff;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.buyButton {
  border-radius: 4px;
  opacity: 1;
  background: #fbbf24;
  width: 88px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.functionButton {
  border-radius: 9999px;
  opacity: 1;
  background: rgba(255, 255, 255, 0.2);
  width: 80px;
  height: 28px;
  padding: 4px 12px;
  display: flex;
  align-items: center;
  justify-content: center;

  font-family: Roboto;
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  letter-spacing: 0px;
}
</style>
